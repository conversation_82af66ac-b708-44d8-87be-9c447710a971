import React from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { ArrowLeft, Shield, Database, CreditCard, Eye, Lock, Mail } from 'lucide-react';
import Link from 'next/link';

export default function PrivacyPolicyPage() {
  return (
    <div className="min-h-screen bg-background p-4">
      <div className="max-w-4xl mx-auto space-y-6">
        {/* Header */}
        <div className="flex items-center gap-4 mb-8">
          <Link href="/">
            <Button variant="ghost" size="sm" className="gap-2">
              <ArrowLeft className="h-4 w-4" />
              Back to App
            </Button>
          </Link>
        </div>

        {/* Main Title */}
        <Card className="bg-card-gradient">
          <CardHeader className="text-center">
            <div className="flex justify-center mb-4">
              <div className="flex h-16 w-16 items-center justify-center rounded-full bg-primary/10">
                <Shield className="h-8 w-8 text-primary" />
              </div>
            </div>
            <CardTitle className="text-3xl">Privacy Policy</CardTitle>
            <CardDescription>
              Last updated: {new Date().toLocaleDateString('en-US', {
                year: 'numeric',
                month: 'long',
                day: 'numeric'
              })}
            </CardDescription>
          </CardHeader>
        </Card>

        {/* Introduction */}
        <Card className="bg-card-gradient">
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Eye className="h-5 w-5" />
              Introduction
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <p className="text-muted-foreground leading-relaxed">
              Welcome to ExpenseFlow. We are committed to protecting your privacy and ensuring the security of your personal information.
              This Privacy Policy explains how we collect, use, store, and protect your information when you use our expense tracking application.
            </p>
            <p className="text-muted-foreground leading-relaxed">
              By using ExpenseFlow, you agree to the collection and use of information in accordance with this policy.
              We will not use or share your information with anyone except as described in this Privacy Policy.
            </p>
          </CardContent>
        </Card>

        {/* Information We Collect */}
        <Card className="bg-card-gradient">
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Database className="h-5 w-5" />
              Information We Collect
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-6">
            <div>
              <h3 className="font-semibold mb-3">Personal Information</h3>
              <ul className="space-y-2 text-muted-foreground ml-4">
                <li className="flex items-start gap-2">
                  <span className="w-1.5 h-1.5 bg-primary rounded-full mt-2 flex-shrink-0"></span>
                  <span><strong>Account Information:</strong> Email address and authentication credentials when you create an account</span>
                </li>
                <li className="flex items-start gap-2">
                  <span className="w-1.5 h-1.5 bg-primary rounded-full mt-2 flex-shrink-0"></span>
                  <span><strong>Financial Data:</strong> Transaction records, wallet information, categories, and budgets you create within the app</span>
                </li>
                <li className="flex items-start gap-2">
                  <span className="w-1.5 h-1.5 bg-primary rounded-full mt-2 flex-shrink-0"></span>
                  <span><strong>Usage Data:</strong> Information about how you use the app, including features accessed and preferences set</span>
                </li>
              </ul>
            </div>

            <div>
              <h3 className="font-semibold mb-3">Automatically Collected Information</h3>
              <ul className="space-y-2 text-muted-foreground ml-4">
                <li className="flex items-start gap-2">
                  <span className="w-1.5 h-1.5 bg-primary rounded-full mt-2 flex-shrink-0"></span>
                  <span><strong>Device Information:</strong> Device type, operating system, and browser information</span>
                </li>
                <li className="flex items-start gap-2">
                  <span className="w-1.5 h-1.5 bg-primary rounded-full mt-2 flex-shrink-0"></span>
                  <span><strong>Log Data:</strong> IP address, access times, and pages viewed for security and troubleshooting purposes</span>
                </li>
              </ul>
            </div>
          </CardContent>
        </Card>

        {/* How We Use Your Information */}
        <Card className="bg-card-gradient">
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Lock className="h-5 w-5" />
              How We Use Your Information
            </CardTitle>
          </CardHeader>
          <CardContent>
            <ul className="space-y-3 text-muted-foreground">
              <li className="flex items-start gap-2">
                <span className="w-1.5 h-1.5 bg-accent rounded-full mt-2 flex-shrink-0"></span>
                <span><strong>Service Provision:</strong> To provide, maintain, and improve ExpenseFlow's functionality</span>
              </li>
              <li className="flex items-start gap-2">
                <span className="w-1.5 h-1.5 bg-accent rounded-full mt-2 flex-shrink-0"></span>
                <span><strong>Account Management:</strong> To create and manage your user account and authenticate your access</span>
              </li>
              <li className="flex items-start gap-2">
                <span className="w-1.5 h-1.5 bg-accent rounded-full mt-2 flex-shrink-0"></span>
                <span><strong>Data Synchronization:</strong> To sync your financial data across your devices securely</span>
              </li>
              <li className="flex items-start gap-2">
                <span className="w-1.5 h-1.5 bg-accent rounded-full mt-2 flex-shrink-0"></span>
                <span><strong>Customer Support:</strong> To respond to your inquiries and provide technical assistance</span>
              </li>
              <li className="flex items-start gap-2">
                <span className="w-1.5 h-1.5 bg-accent rounded-full mt-2 flex-shrink-0"></span>
                <span><strong>Security:</strong> To detect, prevent, and address technical issues and security threats</span>
              </li>
              <li className="flex items-start gap-2">
                <span className="w-1.5 h-1.5 bg-accent rounded-full mt-2 flex-shrink-0"></span>
                <span><strong>Legal Compliance:</strong> To comply with applicable laws and regulations</span>
              </li>
            </ul>
          </CardContent>
        </Card>

        {/* Data Storage and Security */}
        <Card className="bg-card-gradient">
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Database className="h-5 w-5" />
              Data Storage and Security
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <div>
              <h3 className="font-semibold mb-3">Data Storage</h3>
              <p className="text-muted-foreground leading-relaxed mb-3">
                Your financial data is securely stored using Supabase, a trusted cloud database platform.
                All data is encrypted both in transit and at rest using industry-standard encryption protocols.
              </p>
              <ul className="space-y-2 text-muted-foreground ml-4">
                <li className="flex items-start gap-2">
                  <span className="w-1.5 h-1.5 bg-primary rounded-full mt-2 flex-shrink-0"></span>
                  <span>Data is stored in secure, SOC 2 Type II compliant data centers</span>
                </li>
                <li className="flex items-start gap-2">
                  <span className="w-1.5 h-1.5 bg-primary rounded-full mt-2 flex-shrink-0"></span>
                  <span>Regular automated backups ensure data integrity and availability</span>
                </li>
                <li className="flex items-start gap-2">
                  <span className="w-1.5 h-1.5 bg-primary rounded-full mt-2 flex-shrink-0"></span>
                  <span>Access controls and monitoring systems protect against unauthorized access</span>
                </li>
              </ul>
            </div>

            <div>
              <h3 className="font-semibold mb-3">Security Measures</h3>
              <ul className="space-y-2 text-muted-foreground ml-4">
                <li className="flex items-start gap-2">
                  <span className="w-1.5 h-1.5 bg-accent rounded-full mt-2 flex-shrink-0"></span>
                  <span>End-to-end encryption for all data transmission</span>
                </li>
                <li className="flex items-start gap-2">
                  <span className="w-1.5 h-1.5 bg-accent rounded-full mt-2 flex-shrink-0"></span>
                  <span>Secure authentication using industry-standard protocols</span>
                </li>
                <li className="flex items-start gap-2">
                  <span className="w-1.5 h-1.5 bg-accent rounded-full mt-2 flex-shrink-0"></span>
                  <span>Regular security audits and vulnerability assessments</span>
                </li>
                <li className="flex items-start gap-2">
                  <span className="w-1.5 h-1.5 bg-accent rounded-full mt-2 flex-shrink-0"></span>
                  <span>Minimal data collection principle - we only collect what's necessary</span>
                </li>
              </ul>
            </div>
          </CardContent>
        </Card>

        {/* Payment Processing */}
        <Card className="bg-card-gradient">
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <CreditCard className="h-5 w-5" />
              Payment Processing
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <p className="text-muted-foreground leading-relaxed">
              ExpenseFlow uses PayPal for processing premium upgrade payments. We do not store your payment information on our servers.
            </p>
            <ul className="space-y-2 text-muted-foreground ml-4">
              <li className="flex items-start gap-2">
                <span className="w-1.5 h-1.5 bg-primary rounded-full mt-2 flex-shrink-0"></span>
                <span>All payment processing is handled securely by PayPal</span>
              </li>
              <li className="flex items-start gap-2">
                <span className="w-1.5 h-1.5 bg-primary rounded-full mt-2 flex-shrink-0"></span>
                <span>We only receive confirmation of successful payments, not payment details</span>
              </li>
              <li className="flex items-start gap-2">
                <span className="w-1.5 h-1.5 bg-primary rounded-full mt-2 flex-shrink-0"></span>
                <span>PayPal's privacy policy and terms of service apply to payment transactions</span>
              </li>
              <li className="flex items-start gap-2">
                <span className="w-1.5 h-1.5 bg-primary rounded-full mt-2 flex-shrink-0"></span>
                <span>Premium upgrade is a one-time payment with lifetime access</span>
              </li>
            </ul>
          </CardContent>
        </Card>

        {/* Data Sharing and Third Parties */}
        <Card className="bg-card-gradient">
          <CardHeader>
            <CardTitle>Data Sharing and Third Parties</CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <p className="text-muted-foreground leading-relaxed">
              We do not sell, trade, or otherwise transfer your personal information to third parties, except as described below:
            </p>
            <ul className="space-y-3 text-muted-foreground ml-4">
              <li className="flex items-start gap-2">
                <span className="w-1.5 h-1.5 bg-primary rounded-full mt-2 flex-shrink-0"></span>
                <span><strong>Service Providers:</strong> Supabase (database hosting) and PayPal (payment processing) as necessary to provide our services</span>
              </li>
              <li className="flex items-start gap-2">
                <span className="w-1.5 h-1.5 bg-primary rounded-full mt-2 flex-shrink-0"></span>
                <span><strong>Legal Requirements:</strong> When required by law, court order, or government regulation</span>
              </li>
              <li className="flex items-start gap-2">
                <span className="w-1.5 h-1.5 bg-primary rounded-full mt-2 flex-shrink-0"></span>
                <span><strong>Business Transfers:</strong> In the event of a merger, acquisition, or sale of assets (with prior notice)</span>
              </li>
              <li className="flex items-start gap-2">
                <span className="w-1.5 h-1.5 bg-primary rounded-full mt-2 flex-shrink-0"></span>
                <span><strong>Protection:</strong> To protect our rights, property, or safety, or that of our users or others</span>
              </li>
            </ul>
          </CardContent>
        </Card>

        {/* Your Rights and Choices */}
        <Card className="bg-card-gradient">
          <CardHeader>
            <CardTitle>Your Rights and Choices</CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <p className="text-muted-foreground leading-relaxed">
              You have several rights regarding your personal information:
            </p>
            <ul className="space-y-3 text-muted-foreground ml-4">
              <li className="flex items-start gap-2">
                <span className="w-1.5 h-1.5 bg-accent rounded-full mt-2 flex-shrink-0"></span>
                <span><strong>Access:</strong> Request a copy of the personal information we hold about you</span>
              </li>
              <li className="flex items-start gap-2">
                <span className="w-1.5 h-1.5 bg-accent rounded-full mt-2 flex-shrink-0"></span>
                <span><strong>Correction:</strong> Request correction of inaccurate or incomplete information</span>
              </li>
              <li className="flex items-start gap-2">
                <span className="w-1.5 h-1.5 bg-accent rounded-full mt-2 flex-shrink-0"></span>
                <span><strong>Deletion:</strong> Request deletion of your account and associated data</span>
              </li>
              <li className="flex items-start gap-2">
                <span className="w-1.5 h-1.5 bg-accent rounded-full mt-2 flex-shrink-0"></span>
                <span><strong>Data Portability:</strong> Request export of your data in a machine-readable format</span>
              </li>
              <li className="flex items-start gap-2">
                <span className="w-1.5 h-1.5 bg-accent rounded-full mt-2 flex-shrink-0"></span>
                <span><strong>Withdrawal:</strong> Withdraw consent for data processing (may limit app functionality)</span>
              </li>
            </ul>
            <p className="text-muted-foreground leading-relaxed mt-4">
              To exercise these rights, please contact us using the information provided in the "Contact Us" section below.
            </p>
          </CardContent>
        </Card>

        {/* Data Retention */}
        <Card className="bg-card-gradient">
          <CardHeader>
            <CardTitle>Data Retention</CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <p className="text-muted-foreground leading-relaxed">
              We retain your personal information only as long as necessary to provide our services and fulfill the purposes outlined in this policy:
            </p>
            <ul className="space-y-2 text-muted-foreground ml-4">
              <li className="flex items-start gap-2">
                <span className="w-1.5 h-1.5 bg-primary rounded-full mt-2 flex-shrink-0"></span>
                <span><strong>Active Accounts:</strong> Data is retained while your account remains active</span>
              </li>
              <li className="flex items-start gap-2">
                <span className="w-1.5 h-1.5 bg-primary rounded-full mt-2 flex-shrink-0"></span>
                <span><strong>Free Tier:</strong> Transaction history is automatically limited to 2 months</span>
              </li>
              <li className="flex items-start gap-2">
                <span className="w-1.5 h-1.5 bg-primary rounded-full mt-2 flex-shrink-0"></span>
                <span><strong>Account Deletion:</strong> Data is permanently deleted within 30 days of account closure</span>
              </li>
              <li className="flex items-start gap-2">
                <span className="w-1.5 h-1.5 bg-primary rounded-full mt-2 flex-shrink-0"></span>
                <span><strong>Legal Requirements:</strong> Some data may be retained longer if required by law</span>
              </li>
            </ul>
          </CardContent>
        </Card>

        {/* Changes to Privacy Policy */}
        <Card className="bg-card-gradient">
          <CardHeader>
            <CardTitle>Changes to This Privacy Policy</CardTitle>
          </CardHeader>
          <CardContent>
            <p className="text-muted-foreground leading-relaxed mb-4">
              We may update this Privacy Policy from time to time to reflect changes in our practices or for other operational, legal, or regulatory reasons.
              When we make changes, we will:
            </p>
            <ul className="space-y-2 text-muted-foreground ml-4 mb-4">
              <li className="flex items-start gap-2">
                <span className="w-1.5 h-1.5 bg-primary rounded-full mt-2 flex-shrink-0"></span>
                <span>Update the "Last updated" date at the top of this policy</span>
              </li>
              <li className="flex items-start gap-2">
                <span className="w-1.5 h-1.5 bg-primary rounded-full mt-2 flex-shrink-0"></span>
                <span>Notify you of significant changes through the app or via email</span>
              </li>
              <li className="flex items-start gap-2">
                <span className="w-1.5 h-1.5 bg-primary rounded-full mt-2 flex-shrink-0"></span>
                <span>Provide a reasonable notice period before changes take effect</span>
              </li>
            </ul>
            <p className="text-muted-foreground leading-relaxed">
              Your continued use of ExpenseFlow after any changes indicates your acceptance of the updated Privacy Policy.
            </p>
          </CardContent>
        </Card>

        {/* Contact Us */}
        <Card className="bg-card-gradient">
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Mail className="h-5 w-5" />
              Contact Us
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <p className="text-muted-foreground leading-relaxed">
              If you have any questions, concerns, or requests regarding this Privacy Policy or our data practices, please contact us:
            </p>
            <div className="bg-muted/50 rounded-lg p-4 space-y-2">
              <p className="font-semibold">ExpenseFlow Support</p>
              <p className="text-muted-foreground">Email: <EMAIL></p>
              <p className="text-muted-foreground">Response Time: Within 48 hours</p>
            </div>
            <p className="text-muted-foreground leading-relaxed text-sm">
              We are committed to resolving any privacy-related concerns promptly and transparently.
              Your privacy is important to us, and we appreciate your trust in ExpenseFlow.
            </p>
          </CardContent>
        </Card>

        {/* Footer */}
        <div className="text-center py-8">
          <p className="text-muted-foreground text-sm">
            Thank you for using ExpenseFlow. Your financial privacy and security are our top priorities.
          </p>
        </div>
      </div>
    </div>
  );
}