
import {
  ShoppingCart,
  Landmark,
  Zap,
  Car,
  Ticket,
  HeartPulse,
  Briefcase,
  Home,
  Wallet as WalletIcon,
  PiggyBank,
  Banknote,
  CreditCard,
  ArrowRightLeft,
  Gift,
  Coffee,
  Plane,
  GraduationCap,
} from 'lucide-react';
import type { Transaction, Wallet, WalletIconInfo, Budget, CategoryInfo } from './types';

export const ICONS = [
    { name: 'ShoppingCart', icon: ShoppingCart },
    { name: 'Landmark', icon: Landmark },
    { name: 'Zap', icon: Zap },
    { name: 'Car', icon: Car },
    { name: 'Ticket', icon: Ticket },
    { name: 'HeartPulse', icon: HeartPulse },
    { name: 'Briefcase', icon: Briefcase },
    { name: 'Home', icon: Home },
    { name: 'ArrowRightLeft', icon: ArrowRightLeft },
    { name: 'Gift', icon: Gift },
    { name: 'Coffee', icon: Coffee },
    { name: 'Plane', icon: Plane },
    { name: 'GraduationCap', icon: GraduationCap },
];

export const initialCategories: CategoryInfo[] = [
  { value: 'utility', label: 'Utility', iconName: 'Zap', type: 'expense' },
  { value: 'entertainment', label: 'Entertainment', iconName: 'Coffee', type: 'expense' },
  { value: 'job', label: 'Job', iconName: 'Briefcase', type: 'income' },
];

export const WALLET_ICONS: WalletIconInfo[] = [
    { name: 'Wallet', icon: WalletIcon },
    { name: 'PiggyBank', icon: PiggyBank },
    { name: 'Banknote', icon: Banknote },
    { name: 'CreditCard', icon: CreditCard }
];

export const initialWallets: Wallet[] = [
    { id: 'wallet1', name: 'Savings', icon: 'Wallet', balance: 0.00, currency: 'USD'},
];

const now = new Date();
const currentMonth = now.getMonth();
const currentYear = now.getFullYear();

export const initialTransactions: Transaction[] = [
    // Current Month
    // {
    //     id: 'txn1',
    //     type: 'expense',
    //     amount: 55.45,
    //     category: 'groceries',
    //     description: 'Weekly grocery shopping',
    //     walletId: 'wallet1',
    //     date: new Date(now.setDate(now.getDate() - 1))
    // },
    // {
    //     id: 'txn2',
    //     type: 'income',
    //     amount: 2500,
    //     category: 'salary',
    //     description: 'Monthly salary',
    //     walletId: 'wallet1',
    //     date: new Date(now.setDate(now.getDate() - 2))
    // },

    // Previous Month
    //  {
    //     id: 'txn5',
    //     type: 'income',
    //     amount: 2500,
    //     category: 'salary',
    //     description: 'Previous month salary',
    //     walletId: 'wallet1',
    //     date: new Date(currentYear, currentMonth - 1, 2)
    // },
    // {
    //     id: 'txn6',
    //     type: 'expense',
    //     amount: 450,
    //     category: 'rent',
    //     description: 'Monthly rent',
    //     walletId: 'wallet1',
    //     date: new Date(currentYear, currentMonth - 1, 5)
    // },

    // Two Months Ago
    // {
    //     id: 'txn9',
    //     type: 'income',
    //     amount: 2450,
    //     category: 'salary',
    //     description: 'Salary two months ago',
    //     walletId: 'wallet1',
    //     date: new Date(currentYear, currentMonth - 2, 2)
    // },
    // {
    //     id: 'txn10',
    //     type: 'expense',
    //     amount: 450,
    //     category: 'rent',
    //     description: 'Monthly rent',
    //     walletId: 'wallet1',
    //     date: new Date(currentYear, currentMonth - 2, 5)
    // },
];

export const initialBudgets: Budget[] = [
    // { id: 'budget-groceries', category: 'groceries', amount: 400 },
    // { id: 'budget-utilities', category: 'utilities', amount: 150 },
    // { id: 'budget-transport', category: 'transport', amount: 100 },
    // { id: 'budget-entertainment', category: 'entertainment', amount: 80 },
    // { id: 'budget-health', category: 'health', amount: 200 },
    // { id: 'budget-rent', category: 'rent', amount: 1200 },
];
